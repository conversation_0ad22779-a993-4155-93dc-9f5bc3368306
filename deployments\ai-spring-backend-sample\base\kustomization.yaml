apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-spring-backend-sample

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

labels:
- pairs:
    app: ai-spring-backend-sample
    app.kubernetes.io/name: ai-spring-backend-sample
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/managed-by: argocd
    source.repo: ChidhagniConsulting/ai-spring-backend_LABEL
    source.branch: 25/merge_LABEL

commonAnnotations:
  app.kubernetes.io/managed-by: kustomize
  source.commit: 6fabe7be

namePrefix: ""
nameSuffix: ""

images:
- name: registry.digitalocean.com/chidhagni-registry/ai-spring-backend:6fabe7be
  newName: registry.digitalocean.com/chidhagni-registry/ai-spring-backend:6fabe7be

replicas:
- name: ai-spring-backend-sample
  count: 1

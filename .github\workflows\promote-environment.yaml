name: 🚀 Promote Between Environments

on:
  workflow_dispatch:
    inputs:
      project_id:
        description: 'Project ID to promote'
        required: true
        type: string
      source_environment:
        description: 'Source environment'
        required: true
        type: choice
        options:
          - dev
          - staging
        default: dev
      target_environment:
        description: 'Target environment'
        required: true
        type: choice
        options:
          - staging
          - production
        default: staging
      dry_run:
        description: 'Dry run (show what would be done without executing)'
        required: false
        type: boolean
        default: false
  
  repository_dispatch:
    types: [promote-environment]

env:
  GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}

jobs:
  validate-promotion:
    runs-on: [self-hosted, Linux]
    outputs:
      should-promote: ${{ steps.validate.outputs.should-promote }}
      project-id: ${{ steps.validate.outputs.project-id }}
      source-env: ${{ steps.validate.outputs.source-env }}
      target-env: ${{ steps.validate.outputs.target-env }}
      dry-run: ${{ steps.validate.outputs.dry-run }}
    steps:
      - name: 🔍 Validate Promotion Request
        id: validate
        run: |
          echo "=== ENVIRONMENT PROMOTION VALIDATION ==="
          echo "Trigger: ${{ github.event_name }}"
          echo "Repository: ${{ github.repository }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Workflow run ID: ${{ github.run_id }}"
          echo "=================================="

          # Extract inputs based on trigger type
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            PROJECT_ID="${{ github.event.inputs.project_id }}"
            SOURCE_ENV="${{ github.event.inputs.source_environment }}"
            TARGET_ENV="${{ github.event.inputs.target_environment }}"
            DRY_RUN="${{ github.event.inputs.dry_run }}"
          elif [ "${{ github.event_name }}" = "repository_dispatch" ]; then
            PROJECT_ID="${{ github.event.client_payload.project_id }}"
            SOURCE_ENV="${{ github.event.client_payload.source_environment }}"
            TARGET_ENV="${{ github.event.client_payload.target_environment }}"
            DRY_RUN="${{ github.event.client_payload.dry_run }}"
          else
            echo "❌ Unsupported trigger type: ${{ github.event_name }}"
            echo "should-promote=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate required fields
          if [ -z "$PROJECT_ID" ] || [ -z "$SOURCE_ENV" ] || [ -z "$TARGET_ENV" ]; then
            echo "❌ Missing required fields: project_id, source_environment, target_environment"
            echo "should-promote=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate project ID format
          if ! echo "$PROJECT_ID" | grep -qE '^[a-z0-9-]+$'; then
            echo "❌ Invalid project ID format: $PROJECT_ID"
            echo "Project ID must be lowercase alphanumeric with hyphens only"
            echo "should-promote=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate environments
          if [ "$SOURCE_ENV" != "dev" ] && [ "$SOURCE_ENV" != "staging" ]; then
            echo "❌ Invalid source environment: $SOURCE_ENV"
            echo "Supported source environments: dev, staging"
            echo "should-promote=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          if [ "$TARGET_ENV" != "staging" ] && [ "$TARGET_ENV" != "production" ]; then
            echo "❌ Invalid target environment: $TARGET_ENV"
            echo "Supported target environments: staging, production"
            echo "should-promote=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate promotion path
          if [ "$SOURCE_ENV" = "dev" ] && [ "$TARGET_ENV" != "staging" ]; then
            echo "❌ Invalid promotion path: dev can only promote to staging"
            echo "should-promote=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          if [ "$SOURCE_ENV" = "staging" ] && [ "$TARGET_ENV" != "production" ]; then
            echo "❌ Invalid promotion path: staging can only promote to production"
            echo "should-promote=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Set default for dry_run if not provided
          if [ -z "$DRY_RUN" ]; then
            DRY_RUN="false"
          fi

          echo "✅ Promotion validation passed"
          echo "should-promote=true" >> $GITHUB_OUTPUT
          echo "project-id=$PROJECT_ID" >> $GITHUB_OUTPUT
          echo "source-env=$SOURCE_ENV" >> $GITHUB_OUTPUT
          echo "target-env=$TARGET_ENV" >> $GITHUB_OUTPUT
          echo "dry-run=$DRY_RUN" >> $GITHUB_OUTPUT

          echo "📋 Promotion Details:"
          echo "  Project: $PROJECT_ID"
          echo "  Source: $SOURCE_ENV"
          echo "  Target: $TARGET_ENV"
          echo "  Dry Run: $DRY_RUN"

  execute-promotion:
    needs: validate-promotion
    if: needs.validate-promotion.outputs.should-promote == 'true'
    runs-on: [self-hosted, Linux]
    outputs:
      promotion-success: ${{ steps.promote.outputs.success }}
      promoted-image: ${{ steps.promote.outputs.promoted-image }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          fetch-depth: 0

      - name: 🐍 Setup Python Environment
        run: |
          echo "🐍 Checking Python environment..."
          python3 --version || python --version
          
          echo "📦 Installing Python dependencies..."
          pip3 install PyYAML requests --user --quiet || echo "⚠️ Some dependencies may not be available"

      - name: 🚀 Execute Promotion
        id: promote
        run: |
          PROJECT_ID="${{ needs.validate-promotion.outputs.project-id }}"
          SOURCE_ENV="${{ needs.validate-promotion.outputs.source-env }}"
          TARGET_ENV="${{ needs.validate-promotion.outputs.target-env }}"
          DRY_RUN="${{ needs.validate-promotion.outputs.dry-run }}"

          echo "🚀 Starting promotion: $PROJECT_ID from $SOURCE_ENV to $TARGET_ENV"

          # Check if promotion script exists
          if [ ! -f "scripts/promote-image.py" ]; then
            echo "❌ Promotion script not found: scripts/promote-image.py"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Build promotion command
          PROMOTION_CMD="python3 scripts/promote-image.py --project-id $PROJECT_ID --source-env $SOURCE_ENV --target-env $TARGET_ENV"
          
          if [ "$DRY_RUN" = "true" ]; then
            PROMOTION_CMD="$PROMOTION_CMD --dry-run"
          fi

          # Execute promotion
          echo "🔧 Running promotion command: $PROMOTION_CMD"
          if $PROMOTION_CMD; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "✅ Promotion completed successfully"
            
            # Try to extract promoted image info (best effort)
            PROMOTED_IMAGE=$(python3 scripts/promote-image.py --project-id $PROJECT_ID --source-env $SOURCE_ENV --target-env $TARGET_ENV --dry-run 2>/dev/null | grep "Image:" | cut -d' ' -f4 || echo "unknown")
            echo "promoted-image=$PROMOTED_IMAGE" >> $GITHUB_OUTPUT
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Promotion failed"
            exit 1
          fi

      - name: 📋 Generate Promotion Summary
        if: steps.promote.outputs.success == 'true'
        run: |
          PROJECT_ID="${{ needs.validate-promotion.outputs.project-id }}"
          SOURCE_ENV="${{ needs.validate-promotion.outputs.source-env }}"
          TARGET_ENV="${{ needs.validate-promotion.outputs.target-env }}"
          DRY_RUN="${{ needs.validate-promotion.outputs.dry-run }}"
          PROMOTED_IMAGE="${{ steps.promote.outputs.promoted-image }}"

          echo "🎉 Environment Promotion Summary"
          echo "================================"
          echo "Project: $PROJECT_ID"
          echo "Promotion Path: $SOURCE_ENV → $TARGET_ENV"
          echo "Promoted Image: $PROMOTED_IMAGE"
          echo "Dry Run: $DRY_RUN"
          echo "Triggered by: ${{ github.actor }}"
          echo "Workflow Run: ${{ github.run_id }}"
          echo ""
          
          if [ "$DRY_RUN" = "true" ]; then
            echo "ℹ️ This was a dry run - no actual deployment was triggered"
          else
            echo "✅ GitOps deployment has been triggered"
            echo "🔗 Monitor progress in ArgoCD dashboard"
            echo "📋 Check deployment status:"
            echo "   kubectl get application $PROJECT_ID-$TARGET_ENV -n argocd"
          fi

  notify-completion:
    needs: [validate-promotion, execute-promotion]
    if: always()
    runs-on: [self-hosted, Linux]
    steps:
      - name: 🎉 Success Notification
        if: needs.execute-promotion.outputs.promotion-success == 'true'
        run: |
          echo "🎉 Environment promotion completed successfully!"
          echo ""
          echo "📊 Promotion Details:"
          echo "  • Project: ${{ needs.validate-promotion.outputs.project-id }}"
          echo "  • Source Environment: ${{ needs.validate-promotion.outputs.source-env }}"
          echo "  • Target Environment: ${{ needs.validate-promotion.outputs.target-env }}"
          echo "  • Promoted Image: ${{ needs.execute-promotion.outputs.promoted-image }}"
          echo "  • Dry Run: ${{ needs.validate-promotion.outputs.dry-run }}"
          echo ""
          echo "🔗 Next Steps:"
          echo "  • Monitor deployment in ArgoCD dashboard"
          echo "  • Verify application health in target environment"
          echo "  • Run smoke tests if configured"

      - name: ❌ Failure Notification
        if: failure() || needs.execute-promotion.outputs.promotion-success != 'true'
        run: |
          echo "❌ Environment promotion failed"
          echo ""
          echo "📊 Attempted Promotion:"
          echo "  • Project: ${{ needs.validate-promotion.outputs.project-id }}"
          echo "  • Source Environment: ${{ needs.validate-promotion.outputs.source-env }}"
          echo "  • Target Environment: ${{ needs.validate-promotion.outputs.target-env }}"
          echo ""
          echo "🔍 Troubleshooting:"
          echo "  • Check workflow logs for detailed error information"
          echo "  • Verify source environment has a deployed image"
          echo "  • Ensure target environment is properly configured"
          echo "  • Check GitHub token permissions"
          echo ""
          echo "🛠️ Manual Promotion:"
          echo "  python3 scripts/promote-image.py --project-id ${{ needs.validate-promotion.outputs.project-id }} --source-env ${{ needs.validate-promotion.outputs.source-env }} --target-env ${{ needs.validate-promotion.outputs.target-env }} --manual"

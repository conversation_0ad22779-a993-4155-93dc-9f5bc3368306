apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: PLACEHOLDER_PROJECT_ID-staging
  namespace: argocd
  labels:
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID-staging
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: staging
    app-type: PLACEHOLDER_APPLICATION_TYPE
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: PLACEHOLDER_PROJECT_ID-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/PLACEHOLDER_PROJECT_ID/overlays/staging
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: PLACEHOLDER_PROJECT_ID-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Staging environment for PLACEHOLDER_APP_NAME"
  - name: Repository
    value: "https://github.com/PLACEHOLDER_SOURCE_REPO"
  - name: Environment
    value: "staging"
  - name: Application Type
    value: "PLACEHOLDER_APPLICATION_TYPE"
  - name: Source Branch
    value: "PLACEHOLDER_SOURCE_BRANCH"
  - name: Commit SHA
    value: "PLACEHOLDER_COMMIT_SHA"
  - name: Configuration
    value: "Staging configuration with production-like settings"

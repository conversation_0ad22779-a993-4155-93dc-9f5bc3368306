#!/usr/bin/env python3
"""
Test script to verify manifest generation works correctly
"""

import json
import sys
import tempfile
import subprocess
from pathlib import Path

def test_kubectl_available():
    """Test if kubectl is available"""
    try:
        result = subprocess.run(["kubectl", "version", "--client"], 
                              capture_output=True, text=True, check=True)
        print("✅ kubectl is available")
        print(f"   Version: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"❌ kubectl is not available: {e}")
        return False

def test_payload_processing():
    """Test payload processing with a sample payload"""
    
    # Sample payload similar to what the workflow would send
    test_payload = {
        "app_name": "test-app",
        "project_id": "test-app",
        "application_type": "springboot-backend",
        "environment": "dev",
        "docker_image": "nginx",
        "docker_tag": "latest",
        "source_repo": "test/repo",
        "source_branch": "main",
        "commit_sha": "abc123def456"
    }
    
    print("🧪 Testing payload processing...")
    print(f"📦 Test payload: {json.dumps(test_payload, indent=2)}")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        payload_file = Path(temp_dir) / "payload.json"
        with open(payload_file, 'w') as f:
            json.dump(test_payload, f)
        
        try:
            # Test the deploy script with dry run
            cmd = [
                "python", "scripts/deploy.py",
                "--payload", json.dumps(test_payload),
                "--output-dir", temp_dir,
                "--dry-run"
            ]
            
            print(f"🚀 Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
            
            print(f"📤 Exit code: {result.returncode}")
            print(f"📤 Stdout:\n{result.stdout}")
            if result.stderr:
                print(f"📤 Stderr:\n{result.stderr}")
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Error running test: {e}")
            return False

def main():
    """Main test function"""
    print("🔍 Testing manifest generation...")
    
    # Test 1: Check kubectl availability
    kubectl_ok = test_kubectl_available()
    
    # Test 2: Test payload processing
    payload_ok = test_payload_processing()
    
    # Summary
    print("\n📊 Test Results:")
    print(f"   kubectl available: {'✅' if kubectl_ok else '❌'}")
    print(f"   payload processing: {'✅' if payload_ok else '❌'}")
    
    if kubectl_ok and payload_ok:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/bin/bash
set -e

BACKEND=$1
NAMESPACE="ai-react-frontend-dev"

if [ -z "$BACKEND" ]; then
    echo "❌ Backend type required!"
    echo "Usage: $0 {spring|django|nest}"
    exit 1
fi

case $BACKEND in
  "spring")
    URL="http://139.59.50.43:8080"
    SERVICE="ai-spring-backend-service"
    NS="ai-spring-backend-dev"
    HEALTH_ENDPOINT="/actuator/health"
    ;;
  "django")
    URL="http://152.42.157.69:8000"
    SERVICE="ai-django-backend-service"
    NS="ai-django-backend-dev"
    HEALTH_ENDPOINT="/health"
    ;;
  "nest")
    URL="http://139.59.53.144:3000"
    SERVICE="ai-nest-backend-service"
    NS="ai-nest-backend-dev"
    HEALTH_ENDPOINT="/health"
    ;;
  *)
    echo "❌ Invalid backend type: $BACKEND"
    echo "Usage: $0 {spring|django|nest}"
    exit 1
    ;;
esac

echo "🔄 Switching to $BACKEND backend..."

# Get current config and update it
CURRENT_CONFIG=$(kubectl get configmap ai-react-frontend-runtime-config -n $NAMESPACE -o jsonpath='{.data.runtime-config\.json}')
TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%SZ)

# Update the config using jq (preserving supportedBackends)
UPDATED_CONFIG=$(echo "$CURRENT_CONFIG" | jq --arg backend "$BACKEND" --arg url "$URL" --arg service "$SERVICE" --arg ns "$NS" --arg timestamp "$TIMESTAMP" '
  .currentBackend = $backend |
  .backendUrl = $url |
  .serviceName = $service |
  .namespace = $ns |
  .lastUpdated = $timestamp
' | jq -c .)

# Update ConfigMap
kubectl patch configmap ai-react-frontend-runtime-config -n $NAMESPACE --type merge -p "{
  \"data\": {
    \"runtime-config.json\": \"$UPDATED_CONFIG\"
  }
}"

# Restart frontend deployment
kubectl rollout restart deployment ai-react-frontend -n $NAMESPACE

# Wait for rollout to complete
echo "⏳ Waiting for deployment to complete..."
kubectl rollout status deployment ai-react-frontend -n $NAMESPACE --timeout=300s

# Test backend health
echo "🔍 Testing backend health..."
if curl -f -s "$URL$HEALTH_ENDPOINT" > /dev/null 2>&1; then
    echo "✅ $BACKEND backend is healthy ($URL)"
else
    echo "⚠️  $BACKEND backend may not be responding ($URL)"
fi

# Show current config
echo "📋 Current configuration:"
kubectl get configmap ai-react-frontend-runtime-config -n $NAMESPACE -o jsonpath='{.data.runtime-config\.json}' | jq .

echo "🎉 Successfully switched to $BACKEND backend!"

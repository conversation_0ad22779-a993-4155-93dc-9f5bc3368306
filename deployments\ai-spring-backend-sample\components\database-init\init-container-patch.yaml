apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend-sample
  labels:
    app: ai-spring-backend-sample
    app.kubernetes.io/name: ai-spring-backend-sample
    app.kubernetes.io/component: database-init
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/version: 6fabe7be
    app.kubernetes.io/managed-by: argocd
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          # Extract database connection details from environment variables
          DB_HOST_DECODED=$(echo "$DB_HOST_B64" | base64 -d)
          DB_PORT_DECODED=$(echo "$DB_PORT_B64" | base64 -d)
          DB_USER_DECODED=$(echo "$DB_USER_B64" | base64 -d)
          DB_NAME_DECODED=$(echo "$DB_NAME_B64" | base64 -d)

          echo "🔍 Checking database connectivity..."
          echo "Database Host: $DB_HOST_DECODED"
          echo "Database Port: $DB_PORT_DECODED"
          echo "Database User: $DB_USER_DECODED"
          echo "Database Name: $DB_NAME_DECODED"

          # Wait for database to be ready
          until pg_isready -h "$DB_HOST_DECODED" -p "$DB_PORT_DECODED" -U "$DB_USER_DECODED"; do
            echo "⏳ Waiting for database to be ready..."
            sleep 2
          done

          echo "✅ Database is ready!"

          # Optional: Test database connection
          if [ "$TEST_CONNECTION" = "true" ]; then
            echo "🧪 Testing database connection..."
            psql -h "$DB_HOST_DECODED" -p "$DB_PORT_DECODED" -U "$DB_USER_DECODED" -d "$DB_NAME_DECODED" -c "SELECT 1;" > /dev/null
            if [ $? -eq 0 ]; then
              echo "✅ Database connection test successful!"
            else
              echo "❌ Database connection test failed!"
              exit 1
            fi
          fi
        env:
        # Database connection details from secrets
        - name: DB_HOST_B64
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_HOST
        - name: DB_PORT_B64
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_PORT
        - name: DB_USER_B64
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_USER
        - name: DB_NAME_B64
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_NAME
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_PASSWORD
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-sample-secrets
              key: DB_SSL_MODE
        # Application metadata
        - name: APP_NAME
          value: "ai-spring-backend-sample"
        - name: PROJECT_ID
          value: "ai-spring-backend-sample"
        - name: APPLICATION_TYPE
          value: "springboot-backend"
        - name: ENVIRONMENT
          value: "dev"  # This will be overridden by environment-specific patches
        # Optional: Enable connection testing
        - name: TEST_CONNECTION
          value: "false"  # Set to "true" to enable connection testing

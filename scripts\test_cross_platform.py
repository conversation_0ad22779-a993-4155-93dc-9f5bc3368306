#!/usr/bin/env python3
"""
Cross-Platform Deployment Testing Script
Tests that deployment works identically on Windows, Linux, and macOS
"""

import argparse
import os
import sys
import platform
import subprocess
import tempfile
import json
import hashlib
from pathlib import Path


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m✅",
        "ERROR": "\033[31m❌",
        "WARNING": "\033[33m⚠️",
        "INFO": "\033[36mℹ️"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def get_platform_info():
    """Get detailed platform information"""
    return {
        "system": platform.system(),
        "machine": platform.machine(),
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "python_executable": sys.executable
    }


def test_prerequisites():
    """Test that all prerequisites are available"""
    print_status("Testing prerequisites...", "INFO")
    
    # Test Python
    try:
        python_version = platform.python_version()
        print_status(f"Python {python_version} available", "SUCCESS")
    except Exception as e:
        print_status(f"Python test failed: {e}", "ERROR")
        return False
    
    # Test kubectl
    try:
        result = subprocess.run(["kubectl", "version", "--client", "--short"], 
                              capture_output=True, text=True, check=True)
        print_status("kubectl available", "SUCCESS")
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print_status(f"kubectl test failed: {e}", "ERROR")
        return False
    
    # Test required scripts
    required_scripts = ["scripts/deploy.py", "scripts/generate_secrets.py"]
    for script in required_scripts:
        if not Path(script).exists():
            print_status(f"Required script missing: {script}", "ERROR")
            return False
    
    print_status("All prerequisites available", "SUCCESS")
    return True


def test_secret_generation(test_params):
    """Test dynamic secret generation"""
    print_status("Testing dynamic secret generation...", "INFO")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cmd = [
            sys.executable, "scripts/generate_secrets.py",
            "--project-id", test_params["project_id"],
            "--app-type", test_params["app_type"],
            "--environment", test_params["environment"],
            "--output-dir", temp_dir,
            "--deterministic"  # For consistent testing
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Check if patch file was created
            patch_files = list(Path(temp_dir).glob("secret-patch-*.yaml"))
            if not patch_files:
                print_status("Secret patch file not created", "ERROR")
                return False
            
            # Read and validate patch content
            patch_content = patch_files[0].read_text()
            if "DYNAMIC_" in patch_content:
                print_status("Secret patch still contains placeholders", "ERROR")
                return False
            
            print_status("Secret generation test passed", "SUCCESS")
            return True
            
        except subprocess.CalledProcessError as e:
            print_status(f"Secret generation failed: {e.stderr}", "ERROR")
            return False


def test_kustomize_build(test_params):
    """Test Kustomize manifest building"""
    print_status("Testing Kustomize manifest building...", "INFO")
    
    # Test each environment overlay
    environments = ["dev", "staging", "production"]
    for env in environments:
        overlay_path = f"manifests/overlays/{env}"
        if not Path(overlay_path).exists():
            print_status(f"Environment overlay missing: {overlay_path}", "ERROR")
            return False
        
        try:
            result = subprocess.run(["kubectl", "kustomize", overlay_path], 
                                  capture_output=True, text=True, check=True)
            
            # Basic validation of output
            if not result.stdout.strip():
                print_status(f"Empty output for {env} overlay", "ERROR")
                return False
            
            if "apiVersion:" not in result.stdout:
                print_status(f"Invalid YAML output for {env} overlay", "ERROR")
                return False
            
            print_status(f"Kustomize build test passed for {env}", "SUCCESS")
            
        except subprocess.CalledProcessError as e:
            print_status(f"Kustomize build failed for {env}: {e.stderr}", "ERROR")
            return False
    
    return True


def test_unified_deployment_script(test_params):
    """Test the unified deployment script"""
    print_status("Testing unified deployment script...", "INFO")
    
    cmd = [
        sys.executable, "scripts/deploy.py",
        "--project-id", test_params["project_id"],
        "--app-name", test_params["app_name"],
        "--environment", test_params["environment"],
        "--container-image", test_params["container_image"],
        "--app-type", test_params["app_type"],
        "--dry-run"  # Don't actually deploy
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Check for success indicators in output
        if "Deployment completed successfully" not in result.stdout:
            print_status("Deployment script didn't complete successfully", "ERROR")
            return False
        
        # Check if manifest file was generated
        manifest_file = Path(f"generated-manifests/{test_params['project_id']}-{test_params['environment']}.yaml")
        if not manifest_file.exists():
            print_status("Manifest file not generated", "ERROR")
            return False
        
        # Validate manifest content
        manifest_content = manifest_file.read_text()
        if test_params["project_id"] not in manifest_content:
            print_status("Project ID not found in generated manifest", "ERROR")
            return False
        
        print_status("Unified deployment script test passed", "SUCCESS")
        return True
        
    except subprocess.CalledProcessError as e:
        print_status(f"Deployment script failed: {e.stderr}", "ERROR")
        return False


def test_path_handling():
    """Test cross-platform path handling"""
    print_status("Testing cross-platform path handling...", "INFO")
    
    # Test various path operations
    test_paths = [
        "manifests/base/deployment.yaml",
        "manifests/overlays/dev/kustomization.yaml",
        "scripts/deploy.py"
    ]
    
    for test_path in test_paths:
        path_obj = Path(test_path)
        
        # Test path existence
        if not path_obj.exists():
            print_status(f"Path doesn't exist: {test_path}", "ERROR")
            return False
        
        # Test path resolution
        try:
            resolved_path = path_obj.resolve()
            print_status(f"Path resolved: {test_path} -> {resolved_path}", "INFO")
        except Exception as e:
            print_status(f"Path resolution failed for {test_path}: {e}", "ERROR")
            return False
    
    print_status("Path handling test passed", "SUCCESS")
    return True


def generate_platform_report(test_results, platform_info):
    """Generate a platform-specific test report"""
    import datetime

    report = {
        "platform": platform_info,
        "test_results": test_results,
        "timestamp": datetime.datetime.now().isoformat(),
        "summary": {
            "total_tests": len(test_results),
            "passed": sum(1 for result in test_results.values() if result),
            "failed": sum(1 for result in test_results.values() if not result)
        }
    }
    
    # Create report file
    report_file = Path(f"test_report_{platform_info['system'].lower()}.json")
    with open(report_file, "w") as f:
        json.dump(report, f, indent=2)
    
    print_status(f"Platform report generated: {report_file}", "INFO")
    return report


def main():
    parser = argparse.ArgumentParser(description="Test cross-platform deployment compatibility")
    parser.add_argument("--project-id", default="test-app", help="Test project ID")
    parser.add_argument("--app-name", default="Test Application", help="Test app name")
    parser.add_argument("--environment", default="dev", choices=["dev", "staging", "production"], 
                       help="Test environment")
    parser.add_argument("--container-image", default="nginx:latest", help="Test container image")
    parser.add_argument("--app-type", default="web-app", 
                       choices=["react-frontend", "django-backend", "nest-backend", "springboot-backend", "web-app"],
                       help="Test application type")
    parser.add_argument("--generate-report", action="store_true", help="Generate detailed platform report")
    
    args = parser.parse_args()
    
    print("🧪 Cross-Platform Deployment Testing")
    print("=" * 40)
    
    # Get platform information
    platform_info = get_platform_info()
    print_status(f"Testing on: {platform_info['system']} {platform_info['machine']}", "INFO")
    print_status(f"Python: {platform_info['python_version']}", "INFO")
    
    # Test parameters
    test_params = {
        "project_id": args.project_id,
        "app_name": args.app_name,
        "environment": args.environment,
        "container_image": args.container_image,
        "app_type": args.app_type
    }
    
    # Run tests
    test_results = {}
    
    print("\n" + "=" * 40)
    test_results["prerequisites"] = test_prerequisites()
    
    print("\n" + "=" * 40)
    test_results["path_handling"] = test_path_handling()
    
    print("\n" + "=" * 40)
    test_results["secret_generation"] = test_secret_generation(test_params)
    
    print("\n" + "=" * 40)
    test_results["kustomize_build"] = test_kustomize_build(test_params)
    
    print("\n" + "=" * 40)
    test_results["unified_deployment"] = test_unified_deployment_script(test_params)
    
    # Generate report if requested
    if args.generate_report:
        report = generate_platform_report(test_results, platform_info)
    
    # Summary
    print("\n" + "=" * 40)
    print("🏁 Test Summary")
    print("=" * 40)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    failed_tests = total_tests - passed_tests
    
    for test_name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        color = "SUCCESS" if result else "ERROR"
        print_status(f"{test_name}: {status}", color)
    
    print(f"\nResults: {passed_tests}/{total_tests} tests passed")
    
    if failed_tests == 0:
        print_status("All tests passed! Cross-platform compatibility verified.", "SUCCESS")
        sys.exit(0)
    else:
        print_status(f"{failed_tests} test(s) failed. Cross-platform compatibility issues detected.", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()

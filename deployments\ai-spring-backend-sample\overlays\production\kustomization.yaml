apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: PLACEHOLDER_PROJECT_ID-production

resources:
- ../../base

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: production
    source.repo: PLACEHOLDER_SOURCE_REPO
    source.branch: PLACEHOLDER_SOURCE_BRANCH

patches:
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 3
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: "1000m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: "1Gi"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: "2000m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: "2Gi"

- target:
    kind: ConfigMap
    name: PLACEHOLDER_PROJECT_ID-config
  patch: |-
    - op: replace
      path: /data/NODE_ENV
      value: "production"
    - op: replace
      path: /data/SPRING_PROFILES_ACTIVE
      value: "production"
    - op: replace
      path: /data/DEBUG
      value: "False"
    - op: replace
      path: /data/GENERATE_SOURCEMAP
      value: "false"

- target:
    kind: Service
    name: PLACEHOLDER_PROJECT_ID-service
  patch: |-
    - op: replace
      path: /spec/type
      value: "LoadBalancer"

# Database init container environment-specific patch
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
  patch: |-
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: PLACEHOLDER_PROJECT_ID
    spec:
      template:
        spec:
          initContainers:
          - name: wait-for-database
            env:
            - name: ENVIRONMENT
              value: "production"
            - name: TEST_CONNECTION
              value: "false"

patchesStrategicMerge:
- patch-image.yaml

namePrefix: ""
nameSuffix: "-prod"

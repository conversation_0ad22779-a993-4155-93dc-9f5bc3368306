apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-runtime-config
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: runtime-config
    managed-by: argocd
    version: v1.0.0
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    description: "Runtime backend configuration for dynamic switching"
data:
  runtime-config.json: |
    {
      "currentBackend": "spring",
      "backendUrl": "http://************:8080",
      "environment": "dev",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-dev",
      "apiVersion": "v1",
      "lastUpdated": "2024-01-01T00:00:00Z",
      "supportedBackends": [
        {
          "name": "spring",
          "url": "http://************:8080",
          "internalUrl": "http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080",
          "serviceName": "ai-spring-backend-service",
          "namespace": "ai-spring-backend-dev",
          "status": "ready",
          "healthEndpoint": "/actuator/health"
        },
        {
          "name": "django",
          "url": "http://*************:8000",
          "internalUrl": "http://ai-django-backend-service.ai-django-backend-dev.svc.cluster.local:8000",
          "serviceName": "ai-django-backend-service",
          "namespace": "ai-django-backend-dev",
          "status": "ready",
          "healthEndpoint": "/health"
        },
        {
          "name": "nest",
          "url": "http://139.59.53.144:3000",
          "internalUrl": "http://ai-nest-backend-service.ai-nest-backend-dev.svc.cluster.local:3000",
          "serviceName": "ai-nest-backend-service",
          "namespace": "ai-nest-backend-dev",
          "status": "ready",
          "healthEndpoint": "/health"
        }
      ]
    }

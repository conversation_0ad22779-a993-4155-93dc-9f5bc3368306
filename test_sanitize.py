#!/usr/bin/env python3
"""
Test script to verify the sanitize_label_value function works correctly
"""

import re

def sanitize_label_value(value):
    """
    Sanitize a string to be a valid Kubernetes label value.
    
    Kubernetes label values must:
    - Be 63 characters or less
    - Start and end with alphanumeric characters
    - Contain only alphanumeric characters, hyphens, underscores, and dots
    - Not contain forward slashes or other special characters
    """
    if not value:
        return "unknown"
    
    # Replace invalid characters with hyphens
    # Forward slashes, spaces, and other special chars become hyphens
    sanitized = re.sub(r'[^a-zA-Z0-9._-]', '-', str(value))
    
    # Remove leading/trailing non-alphanumeric characters
    sanitized = re.sub(r'^[^a-zA-Z0-9]+', '', sanitized)
    sanitized = re.sub(r'[^a-zA-Z0-9]+$', '', sanitized)
    
    # Ensure it's not empty after sanitization
    if not sanitized:
        sanitized = "unknown"
    
    # Truncate to 63 characters if needed
    if len(sanitized) > 63:
        sanitized = sanitized[:63]
        # Remove trailing non-alphanumeric after truncation
        sanitized = re.sub(r'[^a-zA-Z0-9]+$', '', sanitized)
    
    return sanitized

# Test cases
test_cases = [
    "25/merge",
    "feature/new-feature",
    "main",
    "develop",
    "release/v1.0.0",
    "hotfix/urgent-fix",
    "feature/user-auth-system",
    "",
    None,
    "a" * 70,  # Long string
    "123-abc",
    "abc-123",
    "-invalid-start",
    "invalid-end-",
    "valid.name_with-chars",
    "special@chars#here!",
]

print("Testing sanitize_label_value function:")
print("=" * 50)

for test_case in test_cases:
    result = sanitize_label_value(test_case)
    print(f"Input:  '{test_case}' -> Output: '{result}'")

print("\n" + "=" * 50)
print("All tests completed!")

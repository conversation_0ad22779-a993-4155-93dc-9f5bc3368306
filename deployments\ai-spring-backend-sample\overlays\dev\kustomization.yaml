apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-spring-backend-sample-dev

resources:
- ../../base

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: ai-spring-backend-sample
    app.kubernetes.io/name: ai-spring-backend-sample
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: 6fabe7be
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting/ai-spring-backend
    source.branch: 25/merge

patches:
- target:
    kind: Deployment
    name: ai-spring-backend-sample
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: "250m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: "256Mi"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: "500m"
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: "512Mi"

- target:
    kind: ConfigMap
    name: ai-spring-backend-sample-config
  patch: |-
    - op: replace
      path: /data/NODE_ENV
      value: "dev"
    - op: replace
      path: /data/SPRING_PROFILES_ACTIVE
      value: "dev"
    - op: replace
      path: /data/DEBUG
      value: "True"
    - op: replace
      path: /data/GENERATE_SOURCEMAP
      value: "true"

# Database init container environment-specific patch
- target:
    kind: Deployment
    name: ai-spring-backend-sample
  patch: |-
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: ai-spring-backend-sample
    spec:
      template:
        spec:
          initContainers:
          - name: wait-for-database
            env:
            - name: ENVIRONMENT
              value: "dev"
            - name: TEST_CONNECTION
              value: "true"

patchesStrategicMerge:
- patch-image.yaml

namePrefix: ""
nameSuffix: "-dev"

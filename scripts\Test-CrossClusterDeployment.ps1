#!/usr/bin/env pwsh

# Cross-Cluster Deployment Validation Script
# This script validates that applications can be deployed across different Kubernetes clusters

param(
    [Parameter(Mandatory=$false)]
    [switch]$Detailed
)

# Function to print colored output
function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️ $Message" -ForegroundColor Yellow }
        default { Write-Host "$Message" }
    }
}

# Function to validate ArgoCD project destinations
function Test-ArgoCDProjectDestinations {
    Write-Host ""
    Write-Host "🔍 Validating ArgoCD Project destinations..." -ForegroundColor Cyan
    
    $projectFile = "manifests/argocd/argocd-project.yaml"
    if (-not (Test-Path $projectFile)) {
        Write-Status "ERROR" "ArgoCD project file not found"
        return $false
    }
    
    $projectContent = Get-Content $projectFile -Raw
    
    # Check for local cluster destination
    if ($projectContent -match "server: https://kubernetes\.default\.svc") {
        Write-Status "SUCCESS" "Local cluster destination configured"
    } else {
        Write-Status "ERROR" "Local cluster destination not found"
        return $false
    }
    
    # Check for DigitalOcean cluster destination
    if ($projectContent -match "server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d\.k8s\.ondigitalocean\.com") {
        Write-Status "SUCCESS" "DigitalOcean cluster destination configured"
    } else {
        Write-Status "ERROR" "DigitalOcean cluster destination not found"
        return $false
    }
    
    # Check for wildcard destination (flexibility)
    if ($projectContent -match "server: '\*'") {
        Write-Status "SUCCESS" "Wildcard cluster destination configured for flexibility"
    } else {
        Write-Status "WARNING" "No wildcard cluster destination found"
    }
    
    # Check namespace permissions
    if ($projectContent -match "namespace: '\*'") {
        Write-Status "SUCCESS" "All namespaces are allowed"
    } else {
        Write-Status "WARNING" "Namespace permissions may be restricted"
    }
    
    return $true
}

# Function to validate environment-specific cluster targeting
function Test-EnvironmentTargeting {
    Write-Host ""
    Write-Host "🔍 Validating environment-specific cluster targeting..." -ForegroundColor Cyan
    
    # Dev environment should target local cluster
    $devApp = "manifests/overlays/dev/application.yaml"
    if (Test-Path $devApp) {
        $devContent = Get-Content $devApp -Raw
        if ($devContent -match "server: https://kubernetes\.default\.svc" -and $devContent -match "namespace: dev") {
            Write-Status "SUCCESS" "Dev environment targets local cluster with 'dev' namespace"
        } else {
            Write-Status "ERROR" "Dev environment cluster/namespace targeting incorrect"
            return $false
        }
    } else {
        Write-Status "ERROR" "Dev application file not found"
        return $false
    }
    
    # Staging environment should target DigitalOcean cluster
    $stagingApp = "manifests/overlays/staging/application.yaml"
    if (Test-Path $stagingApp) {
        $stagingContent = Get-Content $stagingApp -Raw
        if ($stagingContent -match "server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d\.k8s\.ondigitalocean\.com" -and $stagingContent -match "namespace: staging") {
            Write-Status "SUCCESS" "Staging environment targets DigitalOcean cluster with 'staging' namespace"
        } else {
            Write-Status "ERROR" "Staging environment cluster/namespace targeting incorrect"
            return $false
        }
    } else {
        Write-Status "ERROR" "Staging application file not found"
        return $false
    }
    
    # Production environment should target DigitalOcean cluster
    $prodApp = "manifests/overlays/production/application.yaml"
    if (Test-Path $prodApp) {
        $prodContent = Get-Content $prodApp -Raw
        if ($prodContent -match "server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d\.k8s\.ondigitalocean\.com" -and $prodContent -match "namespace: production") {
            Write-Status "SUCCESS" "Production environment targets DigitalOcean cluster with 'production' namespace"
        } else {
            Write-Status "ERROR" "Production environment cluster/namespace targeting incorrect"
            return $false
        }
    } else {
        Write-Status "ERROR" "Production application file not found"
        return $false
    }
    
    return $true
}

# Function to validate namespace creation configuration
function Test-NamespaceCreation {
    Write-Host ""
    Write-Host "🔍 Validating namespace creation configuration..." -ForegroundColor Cyan
    
    $environments = @("dev", "staging", "production")
    $namespaceErrors = 0
    
    foreach ($env in $environments) {
        $appFile = "manifests/overlays/$env/application.yaml"
        if (Test-Path $appFile) {
            $appContent = Get-Content $appFile -Raw
            if ($appContent -match "CreateNamespace=true") {
                Write-Status "SUCCESS" "$env environment has automatic namespace creation enabled"
            } else {
                Write-Status "ERROR" "$env environment missing CreateNamespace=true"
                $namespaceErrors++
            }
        } else {
            Write-Status "ERROR" "$env application file not found"
            $namespaceErrors++
        }
    }
    
    if ($namespaceErrors -eq 0) {
        Write-Status "SUCCESS" "All environments have proper namespace creation configured"
        return $true
    } else {
        Write-Status "ERROR" "$namespaceErrors environment(s) have namespace creation issues"
        return $false
    }
}

# Function to validate sync policies per environment
function Test-SyncPolicies {
    Write-Host ""
    Write-Host "🔍 Validating sync policies per environment..." -ForegroundColor Cyan
    
    # Dev should have automated sync
    $devApp = "manifests/overlays/dev/application.yaml"
    if (Test-Path $devApp) {
        $devContent = Get-Content $devApp -Raw
        if ($devContent -match "prune: true" -and $devContent -match "selfHeal: true") {
            Write-Status "SUCCESS" "Dev environment has automated sync with prune and selfHeal enabled"
        } else {
            Write-Status "WARNING" "Dev environment sync policy may not be optimal"
        }
    }
    
    # Staging should have automated sync
    $stagingApp = "manifests/overlays/staging/application.yaml"
    if (Test-Path $stagingApp) {
        $stagingContent = Get-Content $stagingApp -Raw
        if ($stagingContent -match "prune: true" -and $stagingContent -match "selfHeal: true") {
            Write-Status "SUCCESS" "Staging environment has automated sync with prune and selfHeal enabled"
        } else {
            Write-Status "WARNING" "Staging environment sync policy may not be optimal"
        }
    }
    
    # Production should have more conservative sync
    $prodApp = "manifests/overlays/production/application.yaml"
    if (Test-Path $prodApp) {
        $prodContent = Get-Content $prodApp -Raw
        if ($prodContent -match "prune: false" -and $prodContent -match "selfHeal: false") {
            Write-Status "SUCCESS" "Production environment has conservative sync policy (manual control)"
        } else {
            Write-Status "WARNING" "Production environment may have aggressive automation (review recommended)"
        }
    }
    
    return $true
}

# Function to validate RBAC and security
function Test-RBACAndSecurity {
    Write-Host ""
    Write-Host "🔍 Validating RBAC and security configuration..." -ForegroundColor Cyan
    
    $projectFile = "manifests/argocd/argocd-project.yaml"
    $projectContent = Get-Content $projectFile -Raw
    
    # Check for RBAC roles
    if ($projectContent -match "roles:") {
        Write-Status "SUCCESS" "RBAC roles are configured"
        
        # Check for admin role
        if ($projectContent -match "name: admin" -and $projectContent -match "policies:") {
            Write-Status "SUCCESS" "Admin role with policies configured"
        } else {
            Write-Status "WARNING" "Admin role may not have proper policies"
        }
        
        # Check for developer role
        if ($projectContent -match "name: developer" -and $projectContent -match "policies:") {
            Write-Status "SUCCESS" "Developer role with policies configured"
        } else {
            Write-Status "WARNING" "Developer role may not have proper policies"
        }
    } else {
        Write-Status "WARNING" "No RBAC roles configured"
    }
    
    # Check for resource whitelists
    if ($projectContent -match "clusterResourceWhitelist:") {
        Write-Status "SUCCESS" "Cluster resource whitelist configured"
    } else {
        Write-Status "WARNING" "No cluster resource whitelist found"
    }
    
    if ($projectContent -match "namespaceResourceWhitelist:") {
        Write-Status "SUCCESS" "Namespace resource whitelist configured"
    } else {
        Write-Status "WARNING" "No namespace resource whitelist found"
    }
    
    return $true
}

# Main function
function Main {
    Write-Host "🚀 Cross-Cluster Deployment Validation" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green
    
    # Check if we're in the right directory
    if (-not (Test-Path "manifests")) {
        Write-Status "ERROR" "manifests directory not found. Please run from repository root."
        exit 1
    }
    
    $validationErrors = 0
    
    # Validate ArgoCD project destinations
    if (-not (Test-ArgoCDProjectDestinations)) {
        $validationErrors++
    }
    
    # Validate environment-specific cluster targeting
    if (-not (Test-EnvironmentTargeting)) {
        $validationErrors++
    }
    
    # Validate namespace creation
    if (-not (Test-NamespaceCreation)) {
        $validationErrors++
    }
    
    # Validate sync policies (warnings only)
    Test-SyncPolicies | Out-Null
    
    # Validate RBAC and security (warnings only)
    Test-RBACAndSecurity | Out-Null
    
    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    
    if ($validationErrors -eq 0) {
        Write-Status "SUCCESS" "Cross-cluster deployment validation passed! 🎉"
        
        Write-Host ""
        Write-Host "✅ Your cross-cluster deployment configuration is ready!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Summary:" -ForegroundColor Cyan
        Write-Host "✅ ArgoCD project supports multiple cluster destinations"
        Write-Host "✅ Dev environment targets local cluster (https://kubernetes.default.svc)"
        Write-Host "✅ Staging/Production target DigitalOcean cluster"
        Write-Host "✅ Automatic namespace creation is enabled"
        Write-Host "✅ Environment-specific sync policies are configured"
        Write-Host "✅ RBAC and security configurations are in place"
        
        Write-Host ""
        Write-Host "🚀 Next steps:" -ForegroundColor Yellow
        Write-Host "1. Deploy ArgoCD project: kubectl apply -f manifests/argocd/argocd-project.yaml"
        Write-Host "2. Deploy applications: kubectl apply -f manifests/overlays/*/application.yaml"
        Write-Host "3. Monitor sync status: kubectl get applications -n argocd"
        
        exit 0
    } else {
        Write-Status "ERROR" "$validationErrors validation(s) failed"
        Write-Host ""
        Write-Host "Please fix the errors above before proceeding with cross-cluster deployment."
        exit 1
    }
}

# Run main function
Main

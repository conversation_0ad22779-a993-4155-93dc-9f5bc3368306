#!/usr/bin/env python3
"""
Simple test to check if deploy.py can be imported and run with basic arguments
"""

import sys
import json
from pathlib import Path

# Add scripts directory to path
sys.path.append('scripts')

def test_deploy_import():
    """Test if we can import the deploy module"""
    try:
        import deploy
        print("✅ Successfully imported deploy module")
        return True
    except Exception as e:
        print(f"❌ Failed to import deploy module: {e}")
        return False

def test_deploy_help():
    """Test if we can run deploy.py with --help"""
    import subprocess
    try:
        result = subprocess.run([
            sys.executable, "scripts/deploy.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ deploy.py --help works")
            return True
        else:
            print(f"❌ deploy.py --help failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error running deploy.py --help: {e}")
        return False

def test_payload_format():
    """Test the payload format that's being sent"""
    test_payload = {
        "app_name": "test-app",
        "project_id": "test-app",
        "application_type": "springboot-backend",
        "environment": "dev",
        "docker_image": "nginx",
        "docker_tag": "latest",
        "source_repo": "test/repo",
        "source_branch": "main",
        "commit_sha": "abc123"
    }
    
    try:
        payload_json = json.dumps(test_payload)
        print(f"✅ Test payload JSON: {payload_json}")
        
        # Test if it can be parsed back
        parsed = json.loads(payload_json)
        print(f"✅ Payload can be parsed: {parsed['project_id']}")
        return True
    except Exception as e:
        print(f"❌ Payload format error: {e}")
        return False

def main():
    print("🧪 Testing deploy.py script...")
    
    # Test 1: Import
    import_ok = test_deploy_import()
    
    # Test 2: Help command
    help_ok = test_deploy_help()
    
    # Test 3: Payload format
    payload_ok = test_payload_format()
    
    print("\n📊 Test Results:")
    print(f"   Import: {'✅' if import_ok else '❌'}")
    print(f"   Help command: {'✅' if help_ok else '❌'}")
    print(f"   Payload format: {'✅' if payload_ok else '❌'}")
    
    if import_ok and help_ok and payload_ok:
        print("\n🎉 Basic tests passed!")
        return 0
    else:
        print("\n💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Deploy applications using Kustomize overlays with dynamic configuration
    
.DESCRIPTION
    This script deploys applications using Kustomize overlays instead of template processing.
    It supports dynamic configuration injection from CI/CD pipeline parameters and
    maintains compatibility with the GitOps workflow.
    
.PARAMETER ProjectId
    The project identifier (lowercase alphanumeric with hyphens)
    
.PARAMETER AppName
    The human-readable application name
    
.PARAMETER Environment
    Target environment (dev, staging, production)
    
.PARAMETER ContainerImage
    Full container image with tag (e.g., myapp:v1.0.0)
    
.PARAMETER AppType
    Application type (react-frontend, django-backend, nest-backend, springboot-backend, web-app)
    
.PARAMETER ContainerPort
    Container port (default: 8080)
    
.PARAMETER DbHost
    Database host (default: PLACEHOLDER_DB_HOST)
    
.PARAMETER DbPort
    Database port (default: 25060)
    
.PARAMETER DbName
    Database name (default: PLACEH<PERSON>DER_DB_NAME)
    
.PARAMETER DbUser
    Database user (default: PLACEHOLDER_DB_USER)
    
.PARAMETER ClusterServer
    Target cluster server URL (optional, uses environment default)
    
.PARAMETER OutputDir
    Output directory for generated manifests (default: generated-manifests)
    
.PARAMETER DryRun
    Only generate manifests without applying them
    
.PARAMETER Validate
    Validate generated manifests with kubectl
    
.EXAMPLE
    ./Deploy-WithKustomize.ps1 -ProjectId "myapp" -AppName "My Application" -Environment "dev" -ContainerImage "myapp:v1.0.0"
    
.EXAMPLE
    ./Deploy-WithKustomize.ps1 -ProjectId "backend-api" -AppName "Backend API" -Environment "production" -ContainerImage "backend:v2.1.0" -AppType "springboot-backend" -DbHost "prod-db.example.com" -DryRun
#>

param(
    [Parameter(Mandatory = $true)]
    [ValidatePattern('^[a-z0-9-]+$')]
    [string]$ProjectId,
    
    [Parameter(Mandatory = $true)]
    [string]$AppName,
    
    [Parameter(Mandatory = $true)]
    [ValidateSet('dev', 'staging', 'production')]
    [string]$Environment,
    
    [Parameter(Mandatory = $true)]
    [string]$ContainerImage,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet('react-frontend', 'django-backend', 'nest-backend', 'springboot-backend', 'web-app')]
    [string]$AppType = 'web-app',
    
    [Parameter(Mandatory = $false)]
    [string]$ContainerPort = '8080',
    
    [Parameter(Mandatory = $false)]
    [string]$DbHost = 'PLACEHOLDER_DB_HOST',
    
    [Parameter(Mandatory = $false)]
    [string]$DbPort = '25060',
    
    [Parameter(Mandatory = $false)]
    [string]$DbName = 'PLACEHOLDER_DB_NAME',
    
    [Parameter(Mandatory = $false)]
    [string]$DbUser = 'PLACEHOLDER_DB_USER',
    
    [Parameter(Mandatory = $false)]
    [string]$ClusterServer,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputDir = 'generated-manifests',
    
    [Parameter(Mandatory = $false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory = $false)]
    [switch]$Validate
)

# Function to write colored output
function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️ $Message" -ForegroundColor Yellow }
        "INFO" { Write-Host "ℹ️ $Message" -ForegroundColor Cyan }
        default { Write-Host "$Message" }
    }
}

# Function to get cross-platform path
function Get-CrossPlatformPath {
    param([string]$Path)

    if ($IsWindows -or $PSVersionTable.PSVersion.Major -lt 6) {
        return $Path -replace '/', '\'
    } else {
        return $Path -replace '\\', '/'
    }
}

# Function to detect platform
function Get-PlatformInfo {
    $platformInfo = @{
        IsWindows = $IsWindows -or ($PSVersionTable.PSVersion.Major -lt 6)
        IsLinux = $IsLinux
        IsMacOS = $IsMacOS
        PathSeparator = if ($IsWindows -or $PSVersionTable.PSVersion.Major -lt 6) { '\' } else { '/' }
        ExecutableExtension = if ($IsWindows -or $PSVersionTable.PSVersion.Major -lt 6) { '.exe' } else { '' }
    }

    Write-Status "INFO" "Platform: $($platformInfo | ConvertTo-Json -Compress)"
    return $platformInfo
}

# Function to validate prerequisites
function Test-Prerequisites {
    Write-Status "INFO" "Validating prerequisites..."

    $platformInfo = Get-PlatformInfo

    # Check if kubectl is available
    $kubectlCmd = "kubectl$($platformInfo.ExecutableExtension)"
    try {
        & $kubectlCmd version --client --short | Out-Null
        Write-Status "SUCCESS" "kubectl is available"
    } catch {
        Write-Status "ERROR" "kubectl is not available. Please install kubectl."
        return $false
    }

    # Check if we're in the right directory
    $manifestsPath = Get-CrossPlatformPath "manifests"
    if (-not (Test-Path $manifestsPath)) {
        Write-Status "ERROR" "manifests directory not found. Please run from repository root."
        return $false
    }

    # Check if environment overlay exists
    $overlayPath = Get-CrossPlatformPath "manifests/overlays/$Environment"
    if (-not (Test-Path $overlayPath)) {
        Write-Status "ERROR" "Environment overlay not found: $overlayPath"
        return $false
    }

    Write-Status "SUCCESS" "Prerequisites validation passed"
    return $true
}

# Function to prepare project-specific overlay
function New-ProjectOverlay {
    Write-Status "INFO" "Preparing project-specific overlay..."

    $sourceOverlay = Get-CrossPlatformPath "manifests/overlays/$Environment"
    $projectOverlay = Get-CrossPlatformPath "$ProjectId-$Environment"

    # Remove existing project overlay if it exists
    if (Test-Path $projectOverlay) {
        Remove-Item -Path $projectOverlay -Recurse -Force
    }

    # Copy environment overlay to project-specific directory
    Copy-Item -Path $sourceOverlay -Destination $projectOverlay -Recurse

    Write-Status "SUCCESS" "Created project overlay: $projectOverlay"
    return $projectOverlay
}

# Function to customize Kustomize configuration
function Update-KustomizeConfiguration {
    param(
        [string]$ProjectOverlayPath
    )
    
    Write-Status "INFO" "Customizing Kustomize configuration..."
    
    Push-Location $ProjectOverlayPath
    
    try {
        # Set namespace to project-environment
        kubectl kustomize edit set namespace "$ProjectId-$Environment"
        
        # Set name prefix
        kubectl kustomize edit set nameprefix "$ProjectId-"
        
        # Update image
        kubectl kustomize edit set image "app=$ContainerImage"
        
        # Update labels
        kubectl kustomize edit add label "app:$ProjectId"
        kubectl kustomize edit add label "environment:$Environment"
        kubectl kustomize edit add label "app-type:$AppType"
        
        Write-Status "SUCCESS" "Kustomize configuration updated"
        
        # Show the updated kustomization.yaml
        Write-Status "INFO" "Updated kustomization.yaml:"
        Get-Content "kustomization.yaml" | Write-Host
        
    } finally {
        Pop-Location
    }
}

# Function to apply dynamic configuration
function Set-DynamicConfiguration {
    param(
        [string]$ProjectOverlayPath
    )
    
    Write-Status "INFO" "Applying dynamic configuration..."
    
    $patchFile = "$ProjectOverlayPath/patch-dynamic-config.yaml"
    
    # Create dynamic configuration patch
    $dynamicConfig = @"
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  # Dynamic database configuration
  DB_HOST: "$DbHost"
  DB_PORT: "$DbPort"
  DB_NAME: "$DbName"
  DB_USER: "$DbUser"
  
  # Application configuration
  PORT: "$ContainerPort"
  APP_NAME: "$AppName"
  
  # Environment-specific overrides
  NODE_ENV: "$Environment"
  SPRING_PROFILES_ACTIVE: "$Environment"
  REACT_APP_ENVIRONMENT: "$Environment"
"@
    
    Set-Content -Path $patchFile -Value $dynamicConfig
    
    # Add the patch to kustomization
    $kustomizationPath = "$ProjectOverlayPath/kustomization.yaml"
    $kustomizationContent = Get-Content $kustomizationPath
    
    # Add patchesStrategicMerge if not already present
    if ($kustomizationContent -notmatch "patchesStrategicMerge:") {
        Add-Content -Path $kustomizationPath -Value "`npatchesStrategicMerge:"
    }
    Add-Content -Path $kustomizationPath -Value "- patch-dynamic-config.yaml"
    
    Write-Status "SUCCESS" "Dynamic configuration applied"
}

# Function to build and validate manifests
function Build-Manifests {
    param(
        [string]$ProjectOverlayPath
    )
    
    Write-Status "INFO" "Building and validating Kubernetes manifests..."
    
    # Create output directory
    if (-not (Test-Path $OutputDir)) {
        New-Item -Path $OutputDir -ItemType Directory | Out-Null
    }
    
    $outputFile = "$OutputDir/$ProjectId-$Environment.yaml"
    
    try {
        # Build manifests with Kustomize
        kubectl kustomize $ProjectOverlayPath | Out-File -FilePath $outputFile -Encoding UTF8
        
        if ($Validate) {
            # Validate YAML syntax
            kubectl apply --dry-run=client -f $outputFile | Out-Null
            Write-Status "SUCCESS" "Manifest validation passed"
        }
        
        Write-Status "SUCCESS" "Manifests built successfully: $outputFile"
        
        # Show manifest summary
        Write-Status "INFO" "Generated manifest summary:"
        kubectl kustomize $ProjectOverlayPath | Select-String -Pattern "^(kind|metadata)" | Select-Object -First 20 | Write-Host
        
        return $outputFile
        
    } catch {
        Write-Status "ERROR" "Failed to build manifests: $($_.Exception.Message)"
        return $null
    }
}

# Function to deploy to cluster
function Deploy-ToCluster {
    param(
        [string]$ProjectOverlayPath
    )
    
    if ($DryRun) {
        Write-Status "INFO" "Dry run mode - skipping deployment"
        return
    }
    
    Write-Status "INFO" "Deploying to $Environment cluster..."
    
    try {
        # Apply manifests
        kubectl apply -k $ProjectOverlayPath
        
        # Wait for deployment to be ready
        $deploymentName = "$ProjectId-app"
        $namespace = "$ProjectId-$Environment"
        
        Write-Status "INFO" "Waiting for deployment to be ready..."
        kubectl wait --for=condition=available deployment/$deploymentName -n $namespace --timeout=300s
        
        Write-Status "SUCCESS" "Deployment completed successfully"
        
        # Show deployment status
        Write-Status "INFO" "Deployment status:"
        kubectl get pods -n $namespace -l app="$ProjectId"
        
    } catch {
        Write-Status "ERROR" "Deployment failed: $($_.Exception.Message)"
        throw
    }
}

# Main execution
function Main {
    Write-Host "🚀 Kustomize-based Application Deployment" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
    
    Write-Status "INFO" "Deployment Configuration:"
    Write-Host "  Project ID: $ProjectId"
    Write-Host "  App Name: $AppName"
    Write-Host "  Environment: $Environment"
    Write-Host "  App Type: $AppType"
    Write-Host "  Container Image: $ContainerImage"
    Write-Host "  Container Port: $ContainerPort"
    Write-Host "  Database Host: $DbHost"
    Write-Host "  Database Port: $DbPort"
    Write-Host "  Output Directory: $OutputDir"
    Write-Host "  Dry Run: $DryRun"
    Write-Host ""
    
    try {
        # Validate prerequisites
        if (-not (Test-Prerequisites)) {
            exit 1
        }
        
        # Prepare project-specific overlay
        $projectOverlay = New-ProjectOverlay
        
        # Customize Kustomize configuration
        Update-KustomizeConfiguration -ProjectOverlayPath $projectOverlay
        
        # Apply dynamic configuration
        Set-DynamicConfiguration -ProjectOverlayPath $projectOverlay
        
        # Build and validate manifests
        $outputFile = Build-Manifests -ProjectOverlayPath $projectOverlay
        if (-not $outputFile) {
            exit 1
        }
        
        # Deploy to cluster (if not dry run)
        if (-not $DryRun) {
            Deploy-ToCluster -ProjectOverlayPath $projectOverlay
        }
        
        Write-Host ""
        Write-Status "SUCCESS" "Deployment process completed successfully!"
        Write-Host ""
        Write-Host "📋 Summary:" -ForegroundColor Cyan
        Write-Host "✅ Project: $AppName ($ProjectId)"
        Write-Host "✅ Environment: $Environment"
        Write-Host "✅ Application Type: $AppType"
        Write-Host "✅ Container Image: $ContainerImage"
        Write-Host "✅ Database Host: $DbHost"
        Write-Host "✅ Namespace: $ProjectId-$Environment"
        Write-Host "✅ Generated Manifests: $outputFile"
        
        if (-not $DryRun) {
            Write-Host ""
            Write-Host "🔗 Next Steps:" -ForegroundColor Yellow
            Write-Host "1. Monitor deployment: kubectl get pods -n $ProjectId-$Environment"
            Write-Host "2. Check logs: kubectl logs -n $ProjectId-$Environment -l app=$ProjectId"
            Write-Host "3. Access application: kubectl get svc -n $ProjectId-$Environment"
        }
        
    } catch {
        Write-Status "ERROR" "Deployment failed: $($_.Exception.Message)"
        exit 1
    }
}

# Run main function
Main
